package com.amobear.themepack.presentation.rewards.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.amobear.themepack.R
import com.amobear.themepack.data.model.CoinPackage

@Composable
fun CoinPackageCard(
    coinPackage: CoinPackage,
    onPurchaseClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(width = 98.dp, height = 108.dp)
            .clickable { onPurchaseClick() }
            .clip(RoundedCornerShape(14.dp))
            .background(Color(0xFFFFD900))
            .border(
                width = 4.dp,
                color = Color(0xFFFFD900),
                shape = RoundedCornerShape(14.dp)
            )
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Top section with cream background containing coin info
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .padding(4.dp)
                    .clip(RoundedCornerShape(10.dp))
                    .background(Color(0xFFFFFCEB)),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(2.dp)
                ) {
                    // Coin amount
                    Text(
                        text = "${coinPackage.coins}",
                        color = Color(0xFF392804),
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Normal
                    )

                    // "Coins" text
                    Text(
                        text = stringResource(R.string.coins_text),
                        color = Color(0xFF392804),
                        fontSize = 10.sp,
                        fontWeight = FontWeight.SemiBold
                    )

                    // Coin icon
                    Icon(
                        painter = painterResource(id = coinPackage.iconRes),
                        contentDescription = null,
                        tint = Color.Unspecified,
                        modifier = Modifier.size(width = 24.dp, height = 29.dp)
                    )
                }
            }

            // Bottom section with price
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(26.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = coinPackage.price,
                    color = Color(0xFF524500),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun CoinPackageCardPreview() {
    CoinPackageCard(
        coinPackage = CoinPackage(
            id = "500",
            coins = 500,
            price = "$1.99",
            iconRes = R.drawable.ic_coin
        ),
        onPurchaseClick = {}
    )
}

@Preview(showBackground = true)
@Composable
fun CoinPackageCardLargePreview() {
    CoinPackageCard(
        coinPackage = CoinPackage(
            id = "5000",
            coins = 5000,
            price = "$5.99",
            iconRes = R.drawable.ic_coin
        ),
        onPurchaseClick = {}
    )
} 